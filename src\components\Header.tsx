'use client';

import { useAuth } from './AuthProvider';

interface HeaderProps {
  isDarkMode: boolean;
  onToggleDarkMode: () => void;
}

export default function Header({ isDarkMode, onToggleDarkMode }: HeaderProps) {
  const { user, signOut } = useAuth();

  console.log('Header render - User state:', {
    isLoggedIn: !!user,
    email: user?.email || 'No user'
  });

  const handleSignOut = async () => {
    try {
      console.log('Attempting to sign out...');
      const result = await signOut();
      if (result.error) {
        console.error('Sign out error:', result.error);
        alert('Failed to sign out: ' + result.error.message);
      } else {
        console.log('Sign out successful');
      }
    } catch (error) {
      console.error('Unexpected error during sign out:', error);
      alert('An unexpected error occurred during sign out');
    }
  };
  return (
    <div className={`h-12 border-b flex items-center px-4 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'}`}>
      <div className="flex items-center space-x-4">
        <h1 className={`text-lg font-semibold ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>Trade Locker</h1>
        <div className="flex items-center space-x-2">
          <button type="button" className="px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors">
            Markets
          </button>
          <button type="button" className="px-3 py-1 text-sm bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors">
            Portfolio
          </button>
          <button type="button" className="px-3 py-1 text-sm bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors">
            Orders
          </button>
        </div>
      </div>
      <div className="ml-auto flex items-center space-x-3">
        <span className={`text-sm ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>Account: $25,000.00</span>
        <div className="w-2 h-2 bg-green-500 rounded-full" title="Connected"></div>

        {/* User Email and Sign Out */}
        {user && (
          <div className="flex items-center space-x-2">
            <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              {user.email}
            </span>
            <button
              type="button"
              onClick={handleSignOut}
              className="px-3 py-1 text-sm bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
            >
              Sign Out
            </button>
          </div>
        )}
        
        {/* Dark Mode Toggle */}
        <button
          type="button"
          onClick={onToggleDarkMode}
          className="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          title={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
        >
          {isDarkMode ? (
            // Sun icon for light mode
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-yellow-500">
              <circle cx="12" cy="12" r="5"/>
              <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
            </svg>
          ) : (
            // Moon icon for dark mode
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-gray-600">
              <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
            </svg>
          )}
        </button>
      </div>
    </div>
  );
}
