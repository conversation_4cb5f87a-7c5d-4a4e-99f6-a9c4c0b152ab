# Trade Locker

A Next.js trading application with Supabase authentication.

## Authentication Setup

This app uses Supabase for email/password authentication. Follow these steps to set up authentication:

### 1. Create a Supabase Project

1. Go to [https://supabase.com](https://supabase.com) and create a new account or sign in
2. Create a new project
3. Wait for the project to be set up (this may take a few minutes)

### 2. Get Your Supabase Credentials

1. In your Supabase project dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (under "Project URL")
   - **anon public** key (under "Project API keys")

### 3. Configure Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.local.example .env.local
   ```

2. Edit `.env.local` and replace the placeholder values with your actual Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_actual_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_supabase_anon_key
   ```

### 4. Configure Supabase Authentication

1. In your Supabase project dashboard, go to **Authentication** > **Settings**
2. Under **Site URL**, add your local development URL: `http://localhost:3000`
3. Under **Redirect URLs**, add: `http://localhost:3000/**`
4. Make sure **Enable email confirmations** is enabled if you want users to verify their email addresses

### 5. Authentication Features

The app includes:
- **Sign Up**: Create new accounts with email/password
- **Sign In**: Login with existing credentials
- **Sign Out**: Logout functionality in the header
- **Password Reset**: Send password reset emails
- **Session Persistence**: Users stay logged in across browser refreshes
- **Protected Routes**: The main app content is only accessible when authenticated

### 6. How It Works

- When users visit the app without being authenticated, they see a full-screen authentication overlay
- The overlay has three tabs: Login, Create Account, and Forgot Password
- After successful authentication, the overlay disappears and the main app content becomes accessible
- Users can sign out using the "Sign Out" button in the header
- Sessions persist across browser refreshes

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
